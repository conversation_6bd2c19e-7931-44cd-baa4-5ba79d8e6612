/*
 * 指针基础示例
 * 演示指针的声明、初始化和使用
 * ⭐ 这是C语言的重点和难点！
 */

#include <stdio.h>

int main() {
    printf("=== 指针基础概念 ===\n");
    
    // 普通变量
    int num = 42;
    float price = 99.99f;
    char grade = 'A';
    
    // 指针声明和初始化
    int *ptr_num = &num;        // ptr_num指向num的地址
    float *ptr_price = &price;  // ptr_price指向price的地址
    char *ptr_grade = &grade;   // ptr_grade指向grade的地址
    
    printf("=== 变量和地址 ===\n");
    printf("num的值: %d, 地址: %p\n", num, (void*)&num);
    printf("price的值: %.2f, 地址: %p\n", price, (void*)&price);
    printf("grade的值: %c, 地址: %p\n", grade, (void*)&grade);
    
    printf("\n=== 指针的值和指向 ===\n");
    printf("ptr_num的值(地址): %p\n", (void*)ptr_num);
    printf("ptr_num指向的值: %d\n", *ptr_num);
    printf("ptr_price的值(地址): %p\n", (void*)ptr_price);
    printf("ptr_price指向的值: %.2f\n", *ptr_price);
    
    printf("\n=== 通过指针修改变量 ===\n");
    printf("修改前 num = %d\n", num);
    *ptr_num = 100;  // 通过指针修改num的值
    printf("修改后 num = %d\n", num);
    
    printf("\n=== 指针运算 ===\n");
    int arr[] = {10, 20, 30, 40, 50};
    int *ptr_arr = arr;  // 数组名就是首元素的地址
    
    printf("数组元素:\n");
    for (int i = 0; i < 5; i++) {
        printf("arr[%d] = %d, 地址: %p\n", i, arr[i], (void*)&arr[i]);
    }
    
    printf("\n通过指针访问数组:\n");
    for (int i = 0; i < 5; i++) {
        printf("*(ptr_arr + %d) = %d, 地址: %p\n", i, *(ptr_arr + i), (void*)(ptr_arr + i));
    }
    
    printf("\n=== 指针和函数 ===\n");
    int a = 10, b = 20;
    printf("交换前: a = %d, b = %d\n", a, b);
    
    // 通过指针交换两个变量的值
    int *ptr_a = &a, *ptr_b = &b;
    int temp = *ptr_a;
    *ptr_a = *ptr_b;
    *ptr_b = temp;
    
    printf("交换后: a = %d, b = %d\n", a, b);
    
    printf("\n=== 空指针和野指针 ===\n");
    int *null_ptr = NULL;  // 空指针
    printf("空指针的值: %p\n", (void*)null_ptr);
    
    // 注意：不要解引用空指针！
    if (null_ptr != NULL) {
        printf("指针指向的值: %d\n", *null_ptr);
    } else {
        printf("这是一个空指针，不能解引用！\n");
    }
    
    printf("\n=== 指针的指针 ===\n");
    int value = 123;
    int *ptr = &value;
    int **ptr_ptr = &ptr;  // 指向指针的指针
    
    printf("value = %d\n", value);
    printf("*ptr = %d\n", *ptr);
    printf("**ptr_ptr = %d\n", **ptr_ptr);
    printf("ptr的地址: %p\n", (void*)&ptr);
    printf("ptr_ptr的值: %p\n", (void*)ptr_ptr);
    
    return 0;
}

/*
重点知识：
1. 指针存储变量的地址
2. &运算符获取变量地址
3. *运算符解引用指针（获取指针指向的值）
4. 指针运算：ptr+1指向下一个元素
5. 数组名就是首元素的地址
6. 空指针(NULL)不指向任何有效地址
7. 野指针指向未知地址，非常危险
8. 指针的指针用于间接访问

常见错误：
- 解引用空指针或野指针
- 指针越界访问
- 忘记初始化指针
- 混淆指针的值和指针指向的值
*/
