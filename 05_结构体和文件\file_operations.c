/*
 * 文件操作示例
 * 演示文件的读写、追加等操作
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 学生结构体
typedef struct {
    int id;
    char name[50];
    float score;
} Student;

int main() {
    printf("=== 文本文件写入 ===\n");
    
    // 打开文件进行写入
    FILE *file = fopen("students.txt", "w");
    if (file == NULL) {
        printf("无法创建文件！\n");
        return 1;
    }
    
    // 写入文本数据
    fprintf(file, "学生成绩表\n");
    fprintf(file, "学号\t姓名\t成绩\n");
    fprintf(file, "1001\t张三\t85.5\n");
    fprintf(file, "1002\t李四\t92.0\n");
    fprintf(file, "1003\t王五\t78.5\n");
    
    fclose(file);
    printf("数据已写入 students.txt\n");
    
    printf("\n=== 文本文件读取 ===\n");
    
    // 打开文件进行读取
    file = fopen("students.txt", "r");
    if (file == NULL) {
        printf("无法打开文件！\n");
        return 1;
    }
    
    char line[100];
    printf("文件内容:\n");
    while (fgets(line, sizeof(line), file) != NULL) {
        printf("%s", line);
    }
    
    fclose(file);
    
    printf("\n=== 文件追加 ===\n");
    
    // 以追加模式打开文件
    file = fopen("students.txt", "a");
    if (file == NULL) {
        printf("无法打开文件进行追加！\n");
        return 1;
    }
    
    fprintf(file, "1004\t赵六\t88.0\n");
    fclose(file);
    printf("新数据已追加到文件\n");
    
    printf("\n=== 格式化读取 ===\n");
    
    file = fopen("students.txt", "r");
    if (file == NULL) {
        printf("无法打开文件！\n");
        return 1;
    }
    
    char header[100];
    fgets(header, sizeof(header), file);  // 跳过标题
    fgets(header, sizeof(header), file);  // 跳过表头
    
    int id;
    char name[50];
    float score;
    
    printf("解析后的学生数据:\n");
    while (fscanf(file, "%d\t%s\t%f", &id, name, &score) == 3) {
        printf("学号: %d, 姓名: %s, 成绩: %.1f\n", id, name, score);
    }
    
    fclose(file);
    
    printf("\n=== 二进制文件操作 ===\n");
    
    // 创建学生数组
    Student students[] = {
        {1001, "张三", 85.5},
        {1002, "李四", 92.0},
        {1003, "王五", 78.5},
        {1004, "赵六", 88.0}
    };
    int numStudents = sizeof(students) / sizeof(students[0]);
    
    // 写入二进制文件
    file = fopen("students.dat", "wb");
    if (file == NULL) {
        printf("无法创建二进制文件！\n");
        return 1;
    }
    
    fwrite(students, sizeof(Student), numStudents, file);
    fclose(file);
    printf("学生数据已写入二进制文件 students.dat\n");
    
    // 读取二进制文件
    file = fopen("students.dat", "rb");
    if (file == NULL) {
        printf("无法打开二进制文件！\n");
        return 1;
    }
    
    Student readStudents[10];
    int count = fread(readStudents, sizeof(Student), 10, file);
    fclose(file);
    
    printf("从二进制文件读取的数据:\n");
    for (int i = 0; i < count; i++) {
        printf("学号: %d, 姓名: %s, 成绩: %.1f\n", 
               readStudents[i].id, readStudents[i].name, readStudents[i].score);
    }
    
    printf("\n=== 文件定位操作 ===\n");
    
    file = fopen("students.dat", "rb");
    if (file == NULL) {
        printf("无法打开文件！\n");
        return 1;
    }
    
    // 移动到第3个学生记录
    fseek(file, 2 * sizeof(Student), SEEK_SET);
    
    Student student;
    if (fread(&student, sizeof(Student), 1, file) == 1) {
        printf("第3个学生: 学号: %d, 姓名: %s, 成绩: %.1f\n", 
               student.id, student.name, student.score);
    }
    
    // 获取当前文件位置
    long position = ftell(file);
    printf("当前文件位置: %ld 字节\n", position);
    
    // 移动到文件末尾
    fseek(file, 0, SEEK_END);
    long fileSize = ftell(file);
    printf("文件大小: %ld 字节\n", fileSize);
    printf("学生记录数: %ld\n", fileSize / sizeof(Student));
    
    fclose(file);
    
    printf("\n=== 错误处理示例 ===\n");
    
    // 尝试打开不存在的文件
    file = fopen("nonexistent.txt", "r");
    if (file == NULL) {
        perror("打开文件失败");
    } else {
        fclose(file);
    }
    
    // 检查文件是否存在
    file = fopen("students.txt", "r");
    if (file != NULL) {
        printf("students.txt 文件存在\n");
        fclose(file);
    } else {
        printf("students.txt 文件不存在\n");
    }
    
    printf("\n=== 文件操作完成 ===\n");
    printf("生成的文件:\n");
    printf("- students.txt (文本文件)\n");
    printf("- students.dat (二进制文件)\n");
    
    return 0;
}

/*
重点知识：
1. 文件操作模式：
   - "r": 只读
   - "w": 写入（覆盖）
   - "a": 追加
   - "rb", "wb": 二进制模式

2. 文本文件函数：
   - fprintf(): 格式化写入
   - fscanf(): 格式化读取
   - fgets(): 读取一行
   - fputs(): 写入一行

3. 二进制文件函数：
   - fread(): 读取二进制数据
   - fwrite(): 写入二进制数据

4. 文件定位函数：
   - fseek(): 移动文件指针
   - ftell(): 获取当前位置
   - rewind(): 回到文件开头

5. 错误处理：
   - 总是检查fopen()的返回值
   - 使用perror()显示错误信息
   - 及时关闭文件

最佳实践：
- 使用完文件后立即关闭
- 检查所有文件操作的返回值
- 二进制文件适合存储结构化数据
- 文本文件便于人工查看和编辑
*/
