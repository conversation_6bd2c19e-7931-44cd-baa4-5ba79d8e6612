/*
 * 练习1参考答案：变量和运算符
 */

#include <stdio.h>

int main() {
    // 1. 声明并初始化变量
    int a = 15;
    int b = 4;
    
    printf("初始值: a = %d, b = %d\n", a, b);
    
    // 2. 计算基本运算
    printf("\n=== 基本运算 ===\n");
    printf("a + b = %d\n", a + b);
    printf("a - b = %d\n", a - b);
    printf("a * b = %d\n", a * b);
    printf("a / b = %d\n", a / b);
    printf("a %% b = %d\n", a % b);
    
    // 3. 自增自减运算
    printf("\n=== 自增自减运算 ===\n");
    printf("a++ = %d, a现在是 %d\n", a++, a);
    printf("++a = %d, a现在是 %d\n", ++a, a);
    printf("b-- = %d, b现在是 %d\n", b--, b);
    printf("--b = %d, b现在是 %d\n", --b, b);
    
    // 4. 最终值
    printf("\n=== 最终值 ===\n");
    printf("最终: a = %d, b = %d\n", a, b);
    
    return 0;
}
