/*
 * 条件语句示例
 * 演示if-else语句的各种用法
 */

#include <stdio.h>

int main() {
    int score;
    
    printf("请输入考试分数 (0-100): ");
    scanf("%d", &score);
    
    // 简单if语句
    if (score >= 60) {
        printf("恭喜！你及格了！\n");
    }
    
    // if-else语句
    if (score >= 90) {
        printf("等级: 优秀\n");
    } else if (score >= 80) {
        printf("等级: 良好\n");
    } else if (score >= 70) {
        printf("等级: 中等\n");
    } else if (score >= 60) {
        printf("等级: 及格\n");
    } else {
        printf("等级: 不及格\n");
    }
    
    // 嵌套if语句
    if (score >= 0 && score <= 100) {
        if (score >= 95) {
            printf("特别奖励：获得奖学金！\n");
        } else if (score >= 85) {
            printf("奖励：获得表扬！\n");
        }
    } else {
        printf("错误：分数必须在0-100之间！\n");
    }
    
    // 三元运算符（条件运算符）
    char *result = (score >= 60) ? "通过" : "未通过";
    printf("考试结果: %s\n", result);
    
    // 逻辑运算符组合条件
    int age = 18;
    if (score >= 60 && age >= 18) {
        printf("可以申请驾照！\n");
    }
    
    if (score < 60 || age < 18) {
        printf("需要继续努力或等待成年！\n");
    }
    
    return 0;
}

/*
重点知识：
1. if语句用于条件判断
2. else if用于多重条件
3. 可以嵌套使用if语句
4. 三元运算符是if-else的简化形式
5. 使用逻辑运算符组合多个条件
6. 注意条件的优先级和括号的使用
*/
