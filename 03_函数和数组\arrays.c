/*
 * 数组示例
 * 演示一维数组和二维数组的使用
 */

#include <stdio.h>
#include <string.h>

// 函数声明
void printArray(int arr[], int size);
int findMax(int arr[], int size);
void bubbleSort(int arr[], int size);

int main() {
    printf("=== 一维数组示例 ===\n");
    
    // 数组声明和初始化
    int numbers[5] = {10, 20, 30, 40, 50};
    int scores[] = {85, 92, 78, 96, 88};  // 自动确定大小
    int grades[10];  // 声明但未初始化
    
    // 访问数组元素
    printf("第一个数字: %d\n", numbers[0]);
    printf("最后一个数字: %d\n", numbers[4]);
    
    // 修改数组元素
    numbers[2] = 35;
    printf("修改后的第三个数字: %d\n", numbers[2]);
    
    // 遍历数组
    printf("所有数字: ");
    for (int i = 0; i < 5; i++) {
        printf("%d ", numbers[i]);
    }
    printf("\n");
    
    // 使用函数处理数组
    printf("\n=== 数组函数操作 ===\n");
    printf("成绩数组: ");
    printArray(scores, 5);
    
    int maxScore = findMax(scores, 5);
    printf("最高分: %d\n", maxScore);
    
    // 数组排序
    printf("排序前: ");
    printArray(scores, 5);
    bubbleSort(scores, 5);
    printf("排序后: ");
    printArray(scores, 5);
    
    printf("\n=== 字符数组（字符串）===\n");
    
    // 字符串的不同声明方式
    char name1[] = "张三";
    char name2[20] = "李四";
    char name3[20];
    
    // 字符串操作
    strcpy(name3, "王五");  // 复制字符串
    printf("姓名1: %s\n", name1);
    printf("姓名2: %s\n", name2);
    printf("姓名3: %s\n", name3);
    
    // 字符串长度
    printf("name1的长度: %zu\n", strlen(name1));
    
    // 字符串连接
    strcat(name2, "同学");
    printf("连接后: %s\n", name2);
    
    printf("\n=== 二维数组示例 ===\n");
    
    // 二维数组声明和初始化
    int matrix[3][3] = {
        {1, 2, 3},
        {4, 5, 6},
        {7, 8, 9}
    };
    
    // 访问二维数组元素
    printf("矩阵:\n");
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            printf("%d ", matrix[i][j]);
        }
        printf("\n");
    }
    
    // 计算矩阵对角线元素之和
    int diagonalSum = 0;
    for (int i = 0; i < 3; i++) {
        diagonalSum += matrix[i][i];
    }
    printf("主对角线元素之和: %d\n", diagonalSum);
    
    return 0;
}

// 打印数组
void printArray(int arr[], int size) {
    for (int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
}

// 查找数组中的最大值
int findMax(int arr[], int size) {
    int max = arr[0];
    for (int i = 1; i < size; i++) {
        if (arr[i] > max) {
            max = arr[i];
        }
    }
    return max;
}

// 冒泡排序
void bubbleSort(int arr[], int size) {
    for (int i = 0; i < size - 1; i++) {
        for (int j = 0; j < size - 1 - i; j++) {
            if (arr[j] > arr[j + 1]) {
                // 交换元素
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}

/*
重点知识：
1. 数组下标从0开始
2. 数组大小在声明时确定，不能改变
3. 数组名就是数组首元素的地址
4. 字符数组用于存储字符串
5. 二维数组可以看作数组的数组
6. 传递数组给函数时，实际传递的是地址
7. 注意数组越界问题
*/
