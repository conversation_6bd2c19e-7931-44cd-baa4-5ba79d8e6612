/*
 * switch语句示例
 * 演示switch-case语句的用法
 */

#include <stdio.h>

int main() {
    int choice;
    char operator;
    double num1, num2, result;
    
    printf("=== 简单计算器 ===\n");
    printf("请输入第一个数字: ");
    scanf("%lf", &num1);
    
    printf("请选择运算符 (+, -, *, /): ");
    scanf(" %c", &operator);  // 注意空格，用于跳过前面的换行符
    
    printf("请输入第二个数字: ");
    scanf("%lf", &num2);
    
    // switch语句处理不同的运算符
    switch (operator) {
        case '+':
            result = num1 + num2;
            printf("%.2f + %.2f = %.2f\n", num1, num2, result);
            break;
        case '-':
            result = num1 - num2;
            printf("%.2f - %.2f = %.2f\n", num1, num2, result);
            break;
        case '*':
            result = num1 * num2;
            printf("%.2f * %.2f = %.2f\n", num1, num2, result);
            break;
        case '/':
            if (num2 != 0) {
                result = num1 / num2;
                printf("%.2f / %.2f = %.2f\n", num1, num2, result);
            } else {
                printf("错误：除数不能为零！\n");
            }
            break;
        default:
            printf("错误：不支持的运算符！\n");
            break;
    }
    
    printf("\n=== 菜单选择示例 ===\n");
    printf("1. 查看个人信息\n");
    printf("2. 修改密码\n");
    printf("3. 退出系统\n");
    printf("请选择 (1-3): ");
    scanf("%d", &choice);
    
    switch (choice) {
        case 1:
            printf("显示个人信息...\n");
            break;
        case 2:
            printf("修改密码...\n");
            break;
        case 3:
            printf("退出系统，再见！\n");
            break;
        default:
            printf("无效选择，请输入1-3之间的数字！\n");
    }
    
    printf("\n=== 等级评定示例 ===\n");
    char grade;
    printf("请输入成绩等级 (A, B, C, D, F): ");
    scanf(" %c", &grade);
    
    switch (grade) {
        case 'A':
        case 'a':
            printf("优秀！分数范围: 90-100\n");
            break;
        case 'B':
        case 'b':
            printf("良好！分数范围: 80-89\n");
            break;
        case 'C':
        case 'c':
            printf("中等！分数范围: 70-79\n");
            break;
        case 'D':
        case 'd':
            printf("及格！分数范围: 60-69\n");
            break;
        case 'F':
        case 'f':
            printf("不及格！分数范围: 0-59\n");
            break;
        default:
            printf("无效等级！\n");
    }
    
    return 0;
}

/*
重点知识：
1. switch语句适用于多分支选择
2. 每个case后必须有break，否则会继续执行下一个case
3. default处理所有未匹配的情况
4. 可以将多个case组合处理相同逻辑
5. switch只能用于整数、字符等离散值
6. 相比多个if-else，switch更清晰高效
*/
