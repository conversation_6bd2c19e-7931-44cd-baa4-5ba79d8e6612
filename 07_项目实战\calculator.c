/*
 * 项目：简单计算器
 * 功能：实现基本四则运算的计算器程序
 * 作者：C语言学习者
 * 日期：2024-06-24
 */

#include <stdio.h>
#include <stdlib.h>
#include <math.h>

// 函数声明
double add(double a, double b);
double subtract(double a, double b);
double multiply(double a, double b);
double divide(double a, double b);
void showMenu();
void clearScreen();
int getChoice();
double getNumber(const char* prompt);

int main() {
    int choice;
    double num1, num2, result;
    char continueCalc;
    
    printf("=== 简单计算器 ===\n");
    printf("欢迎使用C语言计算器！\n\n");
    
    do {
        showMenu();
        choice = getChoice();
        
        if (choice >= 1 && choice <= 4) {
            num1 = getNumber("请输入第一个数字: ");
            num2 = getNumber("请输入第二个数字: ");
            
            switch (choice) {
                case 1:
                    result = add(num1, num2);
                    printf("%.2f + %.2f = %.2f\n", num1, num2, result);
                    break;
                case 2:
                    result = subtract(num1, num2);
                    printf("%.2f - %.2f = %.2f\n", num1, num2, result);
                    break;
                case 3:
                    result = multiply(num1, num2);
                    printf("%.2f × %.2f = %.2f\n", num1, num2, result);
                    break;
                case 4:
                    if (num2 != 0) {
                        result = divide(num1, num2);
                        printf("%.2f ÷ %.2f = %.2f\n", num1, num2, result);
                    } else {
                        printf("错误：除数不能为零！\n");
                    }
                    break;
            }
        } else if (choice == 5) {
            printf("感谢使用计算器，再见！\n");
            break;
        } else {
            printf("无效选择，请重新输入！\n");
        }
        
        printf("\n是否继续计算？(y/n): ");
        scanf(" %c", &continueCalc);
        
        if (continueCalc == 'y' || continueCalc == 'Y') {
            clearScreen();
        } else {
            printf("感谢使用计算器，再见！\n");
            break;
        }
        
    } while (1);
    
    return 0;
}

// 加法运算
double add(double a, double b) {
    return a + b;
}

// 减法运算
double subtract(double a, double b) {
    return a - b;
}

// 乘法运算
double multiply(double a, double b) {
    return a * b;
}

// 除法运算
double divide(double a, double b) {
    if (b != 0) {
        return a / b;
    } else {
        printf("错误：除数不能为零！\n");
        return 0;
    }
}

// 显示菜单
void showMenu() {
    printf("\n=== 计算器菜单 ===\n");
    printf("1. 加法 (+)\n");
    printf("2. 减法 (-)\n");
    printf("3. 乘法 (×)\n");
    printf("4. 除法 (÷)\n");
    printf("5. 退出\n");
    printf("==================\n");
}

// 清屏函数（跨平台）
void clearScreen() {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

// 获取用户选择
int getChoice() {
    int choice;
    printf("请选择操作 (1-5): ");
    
    while (scanf("%d", &choice) != 1) {
        printf("输入无效，请输入数字 (1-5): ");
        // 清除输入缓冲区
        while (getchar() != '\n');
    }
    
    return choice;
}

// 获取数字输入
double getNumber(const char* prompt) {
    double number;
    printf("%s", prompt);
    
    while (scanf("%lf", &number) != 1) {
        printf("输入无效，请输入一个数字: ");
        // 清除输入缓冲区
        while (getchar() != '\n');
    }
    
    return number;
}

/*
项目特点：
1. 模块化设计 - 每个功能独立成函数
2. 错误处理 - 处理除零、无效输入等情况
3. 用户友好 - 清晰的菜单和提示信息
4. 跨平台 - 支持Windows和Linux的清屏功能
5. 输入验证 - 确保用户输入的有效性

可能的扩展：
1. 添加更多数学函数（平方根、幂运算等）
2. 支持表达式计算（如 "2+3*4"）
3. 添加历史记录功能
4. 支持科学计算
5. 添加单位转换功能
6. 保存计算结果到文件

编译命令：
gcc -Wall -g calculator.c -o calculator -lm

运行：
./calculator
*/
