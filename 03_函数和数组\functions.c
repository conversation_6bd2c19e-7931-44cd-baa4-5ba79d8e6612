/*
 * 函数示例
 * 演示函数的定义、声明和调用
 */

#include <stdio.h>

// 函数声明（原型）
int add(int a, int b);
int subtract(int a, int b);
float divide(float a, float b);
void printMessage(void);
int factorial(int n);
void swap(int *a, int *b);  // 使用指针交换值

// 主函数
int main() {
    printf("=== 函数基础示例 ===\n");
    
    // 调用函数
    int result1 = add(10, 5);
    int result2 = subtract(10, 5);
    float result3 = divide(10.0, 3.0);
    
    printf("10 + 5 = %d\n", result1);
    printf("10 - 5 = %d\n", result2);
    printf("10 / 3 = %.2f\n", result3);
    
    // 调用无返回值函数
    printMessage();
    
    // 递归函数示例
    printf("\n=== 递归函数示例 ===\n");
    int n = 5;
    printf("%d的阶乘 = %d\n", n, factorial(n));
    
    // 指针参数示例
    printf("\n=== 指针参数示例 ===\n");
    int x = 10, y = 20;
    printf("交换前: x = %d, y = %d\n", x, y);
    swap(&x, &y);
    printf("交换后: x = %d, y = %d\n", x, y);
    
    return 0;
}

// 函数定义
int add(int a, int b) {
    return a + b;
}

int subtract(int a, int b) {
    return a - b;
}

float divide(float a, float b) {
    if (b != 0) {
        return a / b;
    } else {
        printf("错误：除数不能为零！\n");
        return 0;
    }
}

void printMessage(void) {
    printf("这是一个无返回值的函数！\n");
}

// 递归函数：计算阶乘
int factorial(int n) {
    if (n <= 1) {
        return 1;  // 基础情况
    } else {
        return n * factorial(n - 1);  // 递归调用
    }
}

// 使用指针交换两个变量的值
void swap(int *a, int *b) {
    int temp = *a;
    *a = *b;
    *b = temp;
}

/*
重点知识：
1. 函数声明告诉编译器函数的存在
2. 函数定义包含具体的实现代码
3. 函数可以有返回值，也可以没有（void）
4. 参数传递分为值传递和地址传递
5. 递归函数必须有终止条件
6. 使用指针参数可以修改原变量的值
7. 函数名就是函数的地址
*/
