/*
 * 动态内存管理示例
 * 演示malloc、calloc、realloc、free的使用
 * ⭐ 内存管理是C语言的核心技能！
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

int main() {
    printf("=== 动态内存分配基础 ===\n");
    
    // 使用malloc分配内存
    int *ptr = (int*)malloc(sizeof(int));
    if (ptr == NULL) {
        printf("内存分配失败！\n");
        return 1;
    }
    
    *ptr = 42;
    printf("动态分配的整数: %d\n", *ptr);
    
    // 释放内存
    free(ptr);
    ptr = NULL;  // 避免野指针
    
    printf("\n=== 动态数组分配 ===\n");
    
    int size = 5;
    int *arr = (int*)malloc(size * sizeof(int));
    if (arr == NULL) {
        printf("数组内存分配失败！\n");
        return 1;
    }
    
    // 初始化数组
    for (int i = 0; i < size; i++) {
        arr[i] = (i + 1) * 10;
    }
    
    printf("动态数组: ");
    for (int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
    
    printf("\n=== 使用calloc分配内存 ===\n");
    
    // calloc会自动初始化为0
    int *zeros = (int*)calloc(5, sizeof(int));
    if (zeros == NULL) {
        printf("calloc分配失败！\n");
        free(arr);
        return 1;
    }
    
    printf("calloc分配的数组（自动初始化为0）: ");
    for (int i = 0; i < 5; i++) {
        printf("%d ", zeros[i]);
    }
    printf("\n");
    
    printf("\n=== 使用realloc调整内存大小 ===\n");
    
    // 扩大数组
    size = 8;
    arr = (int*)realloc(arr, size * sizeof(int));
    if (arr == NULL) {
        printf("realloc失败！\n");
        free(zeros);
        return 1;
    }
    
    // 初始化新增的元素
    for (int i = 5; i < size; i++) {
        arr[i] = (i + 1) * 10;
    }
    
    printf("扩大后的数组: ");
    for (int i = 0; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
    
    printf("\n=== 动态字符串处理 ===\n");
    
    // 动态分配字符串内存
    char *str = (char*)malloc(50 * sizeof(char));
    if (str == NULL) {
        printf("字符串内存分配失败！\n");
        free(arr);
        free(zeros);
        return 1;
    }
    
    strcpy(str, "Hello, ");
    strcat(str, "World!");
    printf("动态字符串: %s\n", str);
    
    // 调整字符串大小
    str = (char*)realloc(str, 100 * sizeof(char));
    if (str == NULL) {
        printf("字符串realloc失败！\n");
        free(arr);
        free(zeros);
        return 1;
    }
    
    strcat(str, " 欢迎学习C语言！");
    printf("扩展后的字符串: %s\n", str);
    
    printf("\n=== 内存泄漏检查 ===\n");
    
    // 模拟内存泄漏（不要在实际代码中这样做！）
    int *leak_ptr = (int*)malloc(sizeof(int));
    *leak_ptr = 999;
    printf("这个指针将造成内存泄漏: %d\n", *leak_ptr);
    // 注意：这里故意不调用free(leak_ptr)来演示内存泄漏
    
    printf("\n=== 正确的内存管理 ===\n");
    
    // 清理所有分配的内存
    free(arr);
    free(zeros);
    free(str);
    
    // 将指针设为NULL，避免野指针
    arr = NULL;
    zeros = NULL;
    str = NULL;
    
    printf("所有内存已正确释放！\n");
    
    printf("\n=== 内存管理最佳实践 ===\n");
    printf("1. 每个malloc/calloc都要对应一个free\n");
    printf("2. 释放内存后将指针设为NULL\n");
    printf("3. 检查内存分配是否成功\n");
    printf("4. 避免重复释放同一块内存\n");
    printf("5. 避免使用已释放的内存\n");
    
    return 0;
}

/*
重点知识：
1. malloc(size) - 分配指定字节数的内存
2. calloc(num, size) - 分配并初始化为0
3. realloc(ptr, new_size) - 调整内存块大小
4. free(ptr) - 释放内存
5. 总是检查内存分配是否成功
6. 释放内存后将指针设为NULL

常见错误：
- 内存泄漏：分配了内存但忘记释放
- 野指针：使用已释放的内存
- 重复释放：对同一块内存调用多次free
- 缓冲区溢出：访问超出分配范围的内存

内存管理原则：
- 谁分配，谁释放
- 成对使用malloc/free
- 防御性编程，检查返回值
*/
