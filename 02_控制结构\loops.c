/*
 * 循环语句示例
 * 演示for、while、do-while循环的用法
 */

#include <stdio.h>

int main() {
    printf("=== for循环示例 ===\n");
    
    // 基本for循环
    printf("1到10的数字：");
    for (int i = 1; i <= 10; i++) {
        printf("%d ", i);
    }
    printf("\n");
    
    // 计算1到100的和
    int sum = 0;
    for (int i = 1; i <= 100; i++) {
        sum += i;
    }
    printf("1到100的和: %d\n", sum);
    
    // 嵌套for循环 - 打印乘法表
    printf("\n=== 九九乘法表 ===\n");
    for (int i = 1; i <= 9; i++) {
        for (int j = 1; j <= i; j++) {
            printf("%d×%d=%d\t", j, i, i*j);
        }
        printf("\n");
    }
    
    printf("\n=== while循环示例 ===\n");
    
    // 基本while循环
    int count = 1;
    printf("倒数：");
    while (count <= 5) {
        printf("%d ", 6 - count);
        count++;
    }
    printf("发射！\n");
    
    // 用while循环计算阶乘
    int n = 5;
    int factorial = 1;
    int temp = n;
    while (temp > 0) {
        factorial *= temp;
        temp--;
    }
    printf("%d的阶乘: %d\n", n, factorial);
    
    printf("\n=== do-while循环示例 ===\n");
    
    // do-while循环（至少执行一次）
    int number;
    do {
        printf("请输入一个正数 (输入0退出): ");
        scanf("%d", &number);
        if (number > 0) {
            printf("你输入的数字是: %d\n", number);
        }
    } while (number != 0);
    
    printf("\n=== 循环控制语句 ===\n");
    
    // break语句 - 跳出循环
    printf("寻找第一个能被7整除的数：");
    for (int i = 1; i <= 100; i++) {
        if (i % 7 == 0) {
            printf("%d\n", i);
            break;  // 找到后立即退出循环
        }
    }
    
    // continue语句 - 跳过本次循环
    printf("1到20中的奇数：");
    for (int i = 1; i <= 20; i++) {
        if (i % 2 == 0) {
            continue;  // 跳过偶数
        }
        printf("%d ", i);
    }
    printf("\n");
    
    return 0;
}

/*
重点知识：
1. for循环适用于已知循环次数的情况
2. while循环适用于条件循环
3. do-while循环至少执行一次
4. break用于跳出循环
5. continue用于跳过当前循环继续下一次
6. 嵌套循环可以处理二维问题
7. 注意避免无限循环
*/
