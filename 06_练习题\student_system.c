/*
 * 练习9参考答案：学生管理系统
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#define MAX_STUDENTS 100

typedef struct {
    int id;
    char name[50];
    int age;
    float score;
} Student;

Student students[MAX_STUDENTS];
int studentCount = 0;

// 函数声明
void addStudent();
void displayAllStudents();
void findStudentById();
void findStudentByName();
float calculateAverageScore();
void showMenu();

int main() {
    int choice;
    
    printf("=== 学生管理系统 ===\n");
    
    while (1) {
        showMenu();
        printf("请选择操作 (1-6): ");
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                addStudent();
                break;
            case 2:
                displayAllStudents();
                break;
            case 3:
                findStudentById();
                break;
            case 4:
                findStudentByName();
                break;
            case 5:
                printf("平均分: %.2f\n", calculateAverageScore());
                break;
            case 6:
                printf("感谢使用学生管理系统！\n");
                return 0;
            default:
                printf("无效选择，请重新输入！\n");
        }
        printf("\n");
    }
    
    return 0;
}

void showMenu() {
    printf("\n=== 菜单 ===\n");
    printf("1. 添加学生\n");
    printf("2. 显示所有学生\n");
    printf("3. 按学号查找\n");
    printf("4. 按姓名查找\n");
    printf("5. 计算平均分\n");
    printf("6. 退出\n");
}

void addStudent() {
    if (studentCount >= MAX_STUDENTS) {
        printf("学生数量已达上限！\n");
        return;
    }
    
    Student *s = &students[studentCount];
    
    printf("请输入学号: ");
    scanf("%d", &s->id);
    
    // 检查学号是否重复
    for (int i = 0; i < studentCount; i++) {
        if (students[i].id == s->id) {
            printf("学号已存在！\n");
            return;
        }
    }
    
    printf("请输入姓名: ");
    scanf("%s", s->name);
    
    printf("请输入年龄: ");
    scanf("%d", &s->age);
    
    printf("请输入成绩: ");
    scanf("%f", &s->score);
    
    studentCount++;
    printf("学生信息添加成功！\n");
}

void displayAllStudents() {
    if (studentCount == 0) {
        printf("暂无学生信息！\n");
        return;
    }
    
    printf("\n=== 所有学生信息 ===\n");
    printf("学号\t姓名\t年龄\t成绩\n");
    printf("--------------------------------\n");
    
    for (int i = 0; i < studentCount; i++) {
        printf("%d\t%s\t%d\t%.1f\n", 
               students[i].id, students[i].name, 
               students[i].age, students[i].score);
    }
}

void findStudentById() {
    if (studentCount == 0) {
        printf("暂无学生信息！\n");
        return;
    }
    
    int id;
    printf("请输入要查找的学号: ");
    scanf("%d", &id);
    
    for (int i = 0; i < studentCount; i++) {
        if (students[i].id == id) {
            printf("找到学生:\n");
            printf("学号: %d\n", students[i].id);
            printf("姓名: %s\n", students[i].name);
            printf("年龄: %d\n", students[i].age);
            printf("成绩: %.1f\n", students[i].score);
            return;
        }
    }
    
    printf("未找到学号为 %d 的学生！\n", id);
}

void findStudentByName() {
    if (studentCount == 0) {
        printf("暂无学生信息！\n");
        return;
    }
    
    char name[50];
    printf("请输入要查找的姓名: ");
    scanf("%s", name);
    
    int found = 0;
    for (int i = 0; i < studentCount; i++) {
        if (strcmp(students[i].name, name) == 0) {
            if (!found) {
                printf("找到学生:\n");
                found = 1;
            }
            printf("学号: %d, 姓名: %s, 年龄: %d, 成绩: %.1f\n", 
                   students[i].id, students[i].name, 
                   students[i].age, students[i].score);
        }
    }
    
    if (!found) {
        printf("未找到姓名为 %s 的学生！\n", name);
    }
}

float calculateAverageScore() {
    if (studentCount == 0) {
        return 0.0;
    }
    
    float total = 0.0;
    for (int i = 0; i < studentCount; i++) {
        total += students[i].score;
    }
    
    return total / studentCount;
}

/*
扩展功能建议：
1. 添加删除学生功能
2. 添加修改学生信息功能
3. 添加按成绩排序功能
4. 添加文件保存和加载功能
5. 添加数据验证（年龄范围、成绩范围等）
*/
