/*
 * 变量和数据类型示例
 * 演示C语言中的基本数据类型和变量声明
 */

#include <stdio.h>

int main() {
    // 整数类型
    int age = 25;
    short height = 175;
    long population = 1400000000L;
    
    // 浮点数类型
    float price = 99.99f;
    double pi = 3.14159265359;
    
    // 字符类型
    char grade = 'A';
    char name[] = "张三";  // 字符串
    
    // 常量
    const int MAX_SCORE = 100;
    
    // 输出变量值
    printf("=== 变量和数据类型演示 ===\n");
    printf("年龄: %d\n", age);
    printf("身高: %d cm\n", height);
    printf("人口: %ld\n", population);
    printf("价格: %.2f 元\n", price);
    printf("圆周率: %.10f\n", pi);
    printf("等级: %c\n", grade);
    printf("姓名: %s\n", name);
    printf("最高分: %d\n", MAX_SCORE);
    
    // 变量大小
    printf("\n=== 数据类型大小 ===\n");
    printf("int: %zu 字节\n", sizeof(int));
    printf("float: %zu 字节\n", sizeof(float));
    printf("double: %zu 字节\n", sizeof(double));
    printf("char: %zu 字节\n", sizeof(char));
    
    return 0;
}

/*
重点知识：
1. 变量必须先声明后使用
2. 不同数据类型占用不同的内存空间
3. 常量用const关键字声明，不能修改
4. printf格式化输出：%d(整数), %f(浮点), %c(字符), %s(字符串)
*/
