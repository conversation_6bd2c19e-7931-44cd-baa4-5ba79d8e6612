/*
 * 第一个C程序 - Hello World
 * 这是学习C语言的第一步
 */

#include <stdio.h>  // 包含标准输入输出库
#ifdef _WIN32
#include <windows.h>
#endif

int main() {
    // 设置控制台编码为UTF-8
    #ifdef _WIN32
    SetConsoleOutputCP(65001);
    #endif

    // 输出Hello World
    printf("Hello, World!\n");
    printf("欢迎学习C语言！\n");
    
    return 0;  // 程序正常结束
}

/*
编译运行方法：
1. 打开命令行，进入文件所在目录
2. 编译：gcc hello.c -o hello
3. 运行：./hello (Linux/Mac) 或 hello.exe (Windows)
*/
