/*
 * 练习2参考答案：判断闰年
 */

#include <stdio.h>

int isLeapYear(int year) {
    // 闰年判断规则：
    // 1. 能被4整除但不能被100整除
    // 2. 或者能被400整除
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        return 1;  // 是闰年
    } else {
        return 0;  // 不是闰年
    }
}

int main() {
    int year;
    
    printf("请输入年份: ");
    scanf("%d", &year);
    
    if (isLeapYear(year)) {
        printf("%d年是闰年\n", year);
    } else {
        printf("%d年不是闰年\n", year);
    }
    
    // 测试一些已知的年份
    printf("\n=== 测试已知年份 ===\n");
    int testYears[] = {2000, 1900, 2004, 2100, 2024};
    int numTests = sizeof(testYears) / sizeof(testYears[0]);
    
    for (int i = 0; i < numTests; i++) {
        printf("%d年: %s\n", testYears[i], 
               isLeapYear(testYears[i]) ? "闰年" : "平年");
    }
    
    return 0;
}
