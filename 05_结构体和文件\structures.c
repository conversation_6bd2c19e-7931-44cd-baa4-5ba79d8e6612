/*
 * 结构体示例
 * 演示结构体的定义、初始化和使用
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

// 结构体定义
struct Student {
    int id;
    char name[50];
    int age;
    float score;
};

// 使用typedef简化结构体名称
typedef struct {
    int x;
    int y;
} Point;

typedef struct {
    Point center;
    int radius;
} Circle;

// 函数声明
void printStudent(struct Student s);
void printStudentPtr(struct Student *s);
float calculateDistance(Point p1, Point p2);

int main() {
    printf("=== 结构体基础使用 ===\n");
    
    // 结构体变量声明和初始化
    struct Student student1 = {1001, "张三", 20, 85.5};
    struct Student student2;
    
    // 逐个赋值
    student2.id = 1002;
    strcpy(student2.name, "李四");
    student2.age = 19;
    student2.score = 92.0;
    
    // 访问结构体成员
    printf("学生1信息:\n");
    printf("学号: %d\n", student1.id);
    printf("姓名: %s\n", student1.name);
    printf("年龄: %d\n", student1.age);
    printf("成绩: %.1f\n", student1.score);
    
    printf("\n学生2信息:\n");
    printStudent(student2);
    
    printf("\n=== 结构体指针 ===\n");
    
    struct Student *ptr = &student1;
    
    // 通过指针访问结构体成员（两种方式）
    printf("通过指针访问（方式1）:\n");
    printf("学号: %d\n", (*ptr).id);
    printf("姓名: %s\n", (*ptr).name);
    
    printf("通过指针访问（方式2）:\n");
    printf("学号: %d\n", ptr->id);
    printf("姓名: %s\n", ptr->name);
    
    // 使用函数处理结构体指针
    printf("\n使用函数打印学生信息:\n");
    printStudentPtr(&student2);
    
    printf("\n=== 结构体数组 ===\n");
    
    struct Student students[3] = {
        {1001, "张三", 20, 85.5},
        {1002, "李四", 19, 92.0},
        {1003, "王五", 21, 78.5}
    };
    
    printf("所有学生信息:\n");
    for (int i = 0; i < 3; i++) {
        printf("学生%d: ", i + 1);
        printStudent(students[i]);
    }
    
    // 计算平均分
    float totalScore = 0;
    for (int i = 0; i < 3; i++) {
        totalScore += students[i].score;
    }
    printf("平均分: %.2f\n", totalScore / 3);
    
    printf("\n=== 嵌套结构体 ===\n");
    
    Point p1 = {3, 4};
    Point p2 = {6, 8};
    
    printf("点1坐标: (%d, %d)\n", p1.x, p1.y);
    printf("点2坐标: (%d, %d)\n", p2.x, p2.y);
    
    float distance = calculateDistance(p1, p2);
    printf("两点间距离: %.2f\n", distance);
    
    // 嵌套结构体
    Circle circle = {{0, 0}, 5};  // 圆心在原点，半径为5
    printf("圆心: (%d, %d), 半径: %d\n", 
           circle.center.x, circle.center.y, circle.radius);
    
    printf("\n=== 动态分配结构体 ===\n");
    
    struct Student *dynamicStudent = (struct Student*)malloc(sizeof(struct Student));
    if (dynamicStudent == NULL) {
        printf("内存分配失败！\n");
        return 1;
    }
    
    // 初始化动态分配的结构体
    dynamicStudent->id = 1004;
    strcpy(dynamicStudent->name, "赵六");
    dynamicStudent->age = 22;
    dynamicStudent->score = 88.0;
    
    printf("动态分配的学生信息:\n");
    printStudentPtr(dynamicStudent);
    
    // 释放内存
    free(dynamicStudent);
    dynamicStudent = NULL;
    
    printf("\n=== 结构体复制 ===\n");
    
    struct Student copy = student1;  // 结构体可以直接赋值
    printf("复制的学生信息:\n");
    printStudent(copy);
    
    // 修改复制的结构体不会影响原结构体
    copy.score = 95.0;
    printf("修改复制后:\n");
    printf("原学生成绩: %.1f\n", student1.score);
    printf("复制学生成绩: %.1f\n", copy.score);
    
    return 0;
}

// 打印学生信息（值传递）
void printStudent(struct Student s) {
    printf("学号: %d, 姓名: %s, 年龄: %d, 成绩: %.1f\n", 
           s.id, s.name, s.age, s.score);
}

// 打印学生信息（指针传递，更高效）
void printStudentPtr(struct Student *s) {
    printf("学号: %d, 姓名: %s, 年龄: %d, 成绩: %.1f\n", 
           s->id, s->name, s->age, s->score);
}

// 计算两点间距离
float calculateDistance(Point p1, Point p2) {
    int dx = p2.x - p1.x;
    int dy = p2.y - p1.y;
    return sqrt(dx * dx + dy * dy);
}

/*
重点知识：
1. 结构体将相关数据组织在一起
2. 使用.运算符访问结构体成员
3. 使用->运算符通过指针访问成员
4. 结构体可以嵌套使用
5. 结构体可以作为函数参数和返回值
6. typedef可以简化结构体类型名
7. 结构体支持直接赋值（浅拷贝）

最佳实践：
- 传递大结构体时使用指针，避免复制开销
- 合理组织结构体成员，考虑内存对齐
- 使用const保护不应修改的结构体参数
*/
