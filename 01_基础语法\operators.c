/*
 * 运算符示例
 * 演示C语言中的各种运算符
 */

#include <stdio.h>

int main() {
    int a = 10, b = 3;
    float x = 10.5, y = 3.2;
    
    printf("=== 算术运算符 ===\n");
    printf("a = %d, b = %d\n", a, b);
    printf("a + b = %d\n", a + b);
    printf("a - b = %d\n", a - b);
    printf("a * b = %d\n", a * b);
    printf("a / b = %d\n", a / b);  // 整数除法
    printf("a %% b = %d\n", a % b);  // 取余
    printf("x / y = %.2f\n", x / y);  // 浮点除法
    
    printf("\n=== 赋值运算符 ===\n");
    int c = 5;
    printf("c = %d\n", c);
    c += 3;  // c = c + 3
    printf("c += 3: %d\n", c);
    c -= 2;  // c = c - 2
    printf("c -= 2: %d\n", c);
    c *= 2;  // c = c * 2
    printf("c *= 2: %d\n", c);
    c /= 3;  // c = c / 3
    printf("c /= 3: %d\n", c);
    
    printf("\n=== 自增自减运算符 ===\n");
    int d = 5;
    printf("d = %d\n", d);
    printf("++d = %d\n", ++d);  // 先自增，后使用
    printf("d++ = %d\n", d++);  // 先使用，后自增
    printf("d = %d\n", d);
    printf("--d = %d\n", --d);  // 先自减，后使用
    printf("d-- = %d\n", d--);  // 先使用，后自减
    printf("d = %d\n", d);
    
    printf("\n=== 比较运算符 ===\n");
    printf("a = %d, b = %d\n", a, b);
    printf("a == b: %d\n", a == b);  // 相等
    printf("a != b: %d\n", a != b);  // 不等
    printf("a > b: %d\n", a > b);    // 大于
    printf("a < b: %d\n", a < b);    // 小于
    printf("a >= b: %d\n", a >= b);  // 大于等于
    printf("a <= b: %d\n", a <= b);  // 小于等于
    
    printf("\n=== 逻辑运算符 ===\n");
    int p = 1, q = 0;  // 1表示真，0表示假
    printf("p = %d, q = %d\n", p, q);
    printf("p && q: %d\n", p && q);  // 逻辑与
    printf("p || q: %d\n", p || q);  // 逻辑或
    printf("!p: %d\n", !p);          // 逻辑非
    printf("!q: %d\n", !q);
    
    return 0;
}

/*
重点知识：
1. 整数除法会截断小数部分
2. 自增自减运算符的前置和后置区别
3. 比较运算符返回1(真)或0(假)
4. 逻辑运算符用于组合条件
5. 赋值运算符可以简化代码
*/
