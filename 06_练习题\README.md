# C语言练习题

## 📝 基础语法练习

### 练习1：变量和运算符
编写程序完成以下任务：
1. 声明两个整数变量a和b，分别赋值为15和4
2. 计算并输出它们的和、差、积、商和余数
3. 使用自增运算符让a增加1，使用自减运算符让b减少1
4. 输出最终的a和b的值

**参考答案**: `basic_operators.c`

### 练习2：条件判断
编写程序判断一个年份是否为闰年：
- 能被4整除但不能被100整除的年份是闰年
- 能被400整除的年份也是闰年
- 其他年份不是闰年

**参考答案**: `leap_year.c`

### 练习3：循环练习
编写程序完成以下任务：
1. 计算1到100之间所有偶数的和
2. 打印1到20的平方表
3. 使用嵌套循环打印以下图案：
```
*
**
***
****
*****
```

**参考答案**: `loop_exercises.c`

## 🔧 函数和数组练习

### 练习4：数组操作
编写程序完成以下功能：
1. 创建一个包含10个整数的数组
2. 编写函数找出数组中的最大值和最小值
3. 编写函数计算数组元素的平均值
4. 编写函数对数组进行排序（选择排序或冒泡排序）

**参考答案**: `array_operations.c`

### 练习5：字符串处理
编写程序完成以下功能：
1. 编写函数计算字符串长度（不使用strlen）
2. 编写函数反转字符串
3. 编写函数检查字符串是否为回文
4. 编写函数统计字符串中各字符的出现次数

**参考答案**: `string_functions.c`

### 练习6：递归函数
编写递归函数完成以下任务：
1. 计算斐波那契数列的第n项
2. 计算n的阶乘
3. 计算两个数的最大公约数（欧几里得算法）
4. 汉诺塔问题的解法

**参考答案**: `recursion_exercises.c`

## 🎯 指针和内存练习

### 练习7：指针基础
编写程序完成以下任务：
1. 使用指针交换两个变量的值
2. 使用指针遍历数组并计算元素和
3. 编写函数使用指针参数返回多个值
4. 实现简单的指针数组操作

**参考答案**: `pointer_exercises.c`

### 练习8：动态内存
编写程序完成以下功能：
1. 动态分配一个整数数组，大小由用户输入
2. 实现动态数组的插入和删除操作
3. 编写函数动态分配二维数组
4. 实现简单的动态字符串操作

**参考答案**: `dynamic_memory.c`

## 📊 结构体练习

### 练习9：学生管理系统
设计一个简单的学生管理系统：
1. 定义学生结构体（学号、姓名、年龄、成绩）
2. 实现添加学生信息功能
3. 实现查找学生功能（按学号或姓名）
4. 实现显示所有学生信息功能
5. 实现计算平均分功能

**参考答案**: `student_system.c`

### 练习10：图书管理
设计图书管理结构体和相关函数：
1. 定义图书结构体（编号、书名、作者、价格、库存）
2. 实现图书信息的增删改查
3. 实现按价格排序功能
4. 实现库存统计功能

**参考答案**: `book_management.c`

## 📁 文件操作练习

### 练习11：文件读写
编写程序完成以下任务：
1. 创建一个文本文件，写入学生成绩数据
2. 从文件中读取数据并计算平均分
3. 将结果追加到原文件末尾
4. 实现简单的文件备份功能

**参考答案**: `file_exercises.c`

### 练习12：二进制文件
编写程序完成以下功能：
1. 将结构体数组保存到二进制文件
2. 从二进制文件读取数据并显示
3. 实现文件中特定记录的查找和修改
4. 统计文件中的记录数量

**参考答案**: `binary_file.c`

## 🏆 综合练习

### 练习13：计算器程序
编写一个简单的计算器程序：
1. 支持基本四则运算
2. 支持括号运算
3. 具有历史记录功能
4. 能够保存和加载计算历史

### 练习14：通讯录管理
编写一个通讯录管理程序：
1. 联系人信息（姓名、电话、邮箱、地址）
2. 添加、删除、修改联系人
3. 按姓名或电话查找联系人
4. 将通讯录保存到文件

### 练习15：简单游戏
选择以下游戏之一实现：
1. 猜数字游戏
2. 简单的文字冒险游戏
3. 井字棋游戏
4. 贪吃蛇游戏（控制台版本）

## 💡 练习建议

1. **循序渐进**: 按照难度顺序完成练习
2. **独立思考**: 先尝试自己解决，再查看参考答案
3. **多次练习**: 重要的概念要反复练习
4. **代码规范**: 注意代码格式和注释
5. **调试技能**: 学会使用调试器和printf调试
6. **扩展思考**: 在基础要求上增加自己的功能

## 🔍 自我检测

完成练习后，问自己以下问题：
- [ ] 代码能正确编译和运行吗？
- [ ] 处理了所有可能的错误情况吗？
- [ ] 内存管理是否正确？
- [ ] 代码是否易读易懂？
- [ ] 能否优化算法或代码结构？

记住：编程是一门实践性很强的技能，只有通过大量练习才能真正掌握！
